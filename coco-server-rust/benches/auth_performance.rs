use criterion::{black_box, criterion_group, criterion_main, BenchmarkId, Criterion};
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::runtime::Runtime;

// 导入需要的模块
use coco_server::app_state::AppState;
use coco_server::auth::jwt_cache::JwtCache;
use coco_server::auth::token_blacklist::TokenBlacklist;
use coco_server::auth::user_claims::UserClaims;
use coco_server::config::config_manager::ConfigManager;
use coco_server::repositories::token_repository::TokenRepository;
use coco_server::services::token_service::TokenService;

/// 创建测试用的UserClaims
fn create_test_claims(user_id: &str) -> UserClaims {
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs() as usize;

    UserClaims::new(
        user_id.to_string(),
        format!("user_{}", user_id),
        vec!["user".to_string()],
        "simple".to_string(),
        now + 3600, // 1小时后过期
        now,
    )
}

/// 创建测试用的JWT令牌
fn create_test_jwt_token(user_id: &str) -> String {
    use jsonwebtoken::{encode, EncodingKey, Header};

    let claims = create_test_claims(user_id);
    let secret = "test_secret_key_for_benchmarking_only";

    encode(
        &Header::default(),
        &claims,
        &EncodingKey::from_secret(secret.as_ref()),
    )
    .unwrap()
}

/// JWT缓存性能基准测试
fn jwt_cache_benchmark(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let mut group = c.benchmark_group("jwt_cache");

    // 测试不同缓存大小的性能
    for cache_size in [100, 1000, 10000].iter() {
        let cache = Arc::new(JwtCache::new_without_cleanup(
            Duration::from_secs(300),
            *cache_size,
        ));

        // 预填充缓存
        for i in 0..*cache_size / 2 {
            let token = create_test_jwt_token(&format!("user_{}", i));
            let claims = create_test_claims(&format!("user_{}", i));
            cache.put(&token, claims);
        }

        group.bench_with_input(
            BenchmarkId::new("cache_hit", cache_size),
            cache_size,
            |b, _| {
                let token = create_test_jwt_token("user_1");
                b.iter(|| black_box(cache.get(&token)));
            },
        );

        group.bench_with_input(
            BenchmarkId::new("cache_miss", cache_size),
            cache_size,
            |b, _| {
                let token = create_test_jwt_token("user_nonexistent");
                b.iter(|| black_box(cache.get(&token)));
            },
        );

        group.bench_with_input(
            BenchmarkId::new("cache_put", cache_size),
            cache_size,
            |b, _| {
                let mut counter = 0;
                b.iter(|| {
                    counter += 1;
                    let token = create_test_jwt_token(&format!("new_user_{}", counter));
                    let claims = create_test_claims(&format!("new_user_{}", counter));
                    black_box(cache.put(&token, claims));
                });
            },
        );
    }

    group.finish();
}

/// JWT验证性能基准测试
fn jwt_validation_benchmark(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let mut group = c.benchmark_group("jwt_validation");

    // 创建应用状态用于测试
    let app_state = rt.block_on(async {
        let config_manager = Arc::new(ConfigManager::new().unwrap());
        let token_repository = Arc::new(TokenRepository::new_with_global_db());
        let token_service = Arc::new(TokenService::new(token_repository.clone()));
        let token_blacklist = Arc::new(TokenBlacklist::new());
        let jwt_cache = Arc::new(JwtCache::default());

        AppState::new_with_global_db(
            config_manager,
            token_repository,
            token_service,
            token_blacklist,
            jwt_cache,
        )
    });

    // 测试不同场景的JWT验证性能
    let valid_token = create_test_jwt_token("test_user");
    let invalid_token = "invalid.jwt.token";

    group.bench_function("valid_jwt_first_time", |b| {
        b.iter(|| {
            rt.block_on(async {
                // 清除缓存确保每次都是首次验证
                app_state.jwt_cache.clear();
                black_box(validate_jwt_token_helper(&valid_token, &app_state).await)
            })
        });
    });

    group.bench_function("valid_jwt_cached", |b| {
        // 预热缓存
        rt.block_on(async {
            let _ = validate_jwt_token_helper(&valid_token, &app_state).await;
        });

        b.iter(|| {
            rt.block_on(async {
                black_box(validate_jwt_token_helper(&valid_token, &app_state).await)
            })
        });
    });

    group.bench_function("invalid_jwt", |b| {
        b.iter(|| {
            rt.block_on(async {
                black_box(validate_jwt_token_helper(invalid_token, &app_state).await)
            })
        });
    });

    group.finish();
}

/// JWT验证辅助函数（模拟中间件中的验证逻辑）
async fn validate_jwt_token_helper(
    token: &str,
    app_state: &AppState,
) -> Result<UserClaims, String> {
    use jsonwebtoken::{decode, DecodingKey, Validation};

    // 检查黑名单
    if app_state.token_blacklist.is_blacklisted(token).await {
        return Err("JWT令牌已被注销".to_string());
    }

    // 尝试从缓存获取
    if let Some(cached_claims) = app_state.jwt_cache.get(token) {
        if !cached_claims.is_expired() {
            return Ok(cached_claims);
        } else {
            app_state.jwt_cache.remove(token);
        }
    }

    // 完整验证
    let secret = "test_secret_key_for_benchmarking_only";
    let validation = Validation::default();

    match decode::<UserClaims>(
        token,
        &DecodingKey::from_secret(secret.as_ref()),
        &validation,
    ) {
        Ok(token_data) => {
            let claims = token_data.claims;

            if claims.is_expired() {
                return Err("JWT令牌已过期".to_string());
            }

            // 缓存验证结果
            app_state.jwt_cache.put(token, claims.clone());
            Ok(claims)
        }
        Err(_) => Err("无效的JWT令牌".to_string()),
    }
}

/// 并发性能基准测试
fn concurrent_jwt_benchmark(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let mut group = c.benchmark_group("concurrent_jwt");

    let app_state = rt.block_on(async {
        let config_manager = Arc::new(ConfigManager::new().unwrap());
        let token_repository = Arc::new(TokenRepository::new_with_global_db());
        let token_service = Arc::new(TokenService::new(token_repository.clone()));
        let token_blacklist = Arc::new(TokenBlacklist::new());
        let jwt_cache = Arc::new(JwtCache::new(Duration::from_secs(300), 10000));

        AppState::new_with_global_db(
            config_manager,
            token_repository,
            token_service,
            token_blacklist,
            jwt_cache,
        )
    });

    // 创建多个不同的JWT令牌
    let tokens: Vec<String> = (0..100)
        .map(|i| create_test_jwt_token(&format!("user_{}", i)))
        .collect();

    for concurrency in [1, 10, 50, 100].iter() {
        group.bench_with_input(
            BenchmarkId::new("concurrent_validation", concurrency),
            concurrency,
            |b, &concurrency| {
                b.iter(|| {
                    rt.block_on(async {
                        let mut handles = Vec::new();

                        for i in 0..concurrency {
                            let token = tokens[i % tokens.len()].clone();
                            let app_state = app_state.clone();

                            let handle = tokio::spawn(async move {
                                validate_jwt_token_helper(&token, &app_state).await
                            });

                            handles.push(handle);
                        }

                        // 等待所有任务完成
                        for handle in handles {
                            let _ = black_box(handle.await);
                        }
                    })
                });
            },
        );
    }

    group.finish();
}

/// 缓存清理性能基准测试
fn cache_cleanup_benchmark(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let mut group = c.benchmark_group("cache_cleanup");

    for cache_size in [1000, 5000, 10000].iter() {
        group.bench_with_input(
            BenchmarkId::new("cleanup_expired", cache_size),
            cache_size,
            |b, &cache_size| {
                b.iter_batched(
                    || {
                        // 设置：创建缓存并填充过期项
                        let cache = JwtCache::new(Duration::from_millis(1), cache_size);
                        rt.block_on(async {
                            for i in 0..cache_size {
                                let token = create_test_jwt_token(&format!("user_{}", i));
                                let claims = create_test_claims(&format!("user_{}", i));
                                cache.put(&token, claims);
                            }
                            // 等待过期
                            tokio::time::sleep(Duration::from_millis(10)).await;
                        });
                        cache
                    },
                    |cache| {
                        // 基准测试：清理过期项
                        black_box(cache.cleanup_expired())
                    },
                    criterion::BatchSize::SmallInput,
                );
            },
        );
    }

    group.finish();
}

criterion_group!(
    benches,
    jwt_cache_benchmark,
    jwt_validation_benchmark,
    concurrent_jwt_benchmark,
    cache_cleanup_benchmark
);
criterion_main!(benches);
